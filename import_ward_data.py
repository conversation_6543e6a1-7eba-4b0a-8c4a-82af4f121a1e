#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import csv
import mysql.connector
from mysql.connector import Error
import time

def connect_to_database():
    """Kết nối đến MySQL database"""
    try:
        connection = mysql.connector.connect(
            host='localhost',
            port=3306,
            database='urbox',
            user='root',
            password='root'
        )
        if connection.is_connected():
            print("✅ Kết nối database thành công!")
            return connection
    except Error as e:
        print(f"❌ Lỗi kết nối database: {e}")
        return None

def import_ward_data():
    """Import dữ liệu từ ward_new.csv vào bảng ward"""
    
    print("🔄 Bắt đầu import dữ liệu ward...")
    
    # Kết nối database
    connection = connect_to_database()
    if not connection:
        return
    
    cursor = connection.cursor()
    
    try:
        # Đọc dữ liệu từ CSV
        print("📖 Đọc dữ liệu từ ward_new.csv...")
        
        insert_count = 0
        update_count = 0
        error_count = 0
        
        with open('ward_new.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            
            for row_num, row in enumerate(reader, 1):
                try:
                    # Mapping dữ liệu từ CSV sang database
                    ward_pti_id = int(row['ward_pti_id']) if row['ward_pti_id'].strip() else 0
                    prefix = row['prefix'].strip()
                    province_pti_id = int(row['province_pti_id']) if row['province_pti_id'].strip() else 0
                    province_title = row['province_title'].strip()
                    ward_title = row['ward_title'].strip()
                    province_id = int(row['province_id']) if row['province_id'].strip() else 0
                    
                    # Tạo title_full
                    title_full = f"{prefix} {ward_title}, {province_title}"
                    
                    # Kiểm tra xem record đã tồn tại chưa (dựa trên pti_id)
                    check_query = "SELECT id FROM ward WHERE pti_id = %s"
                    cursor.execute(check_query, (ward_pti_id,))
                    existing_record = cursor.fetchone()
                    
                    current_time = int(time.time())
                    
                    if existing_record:
                        # Update record hiện tại
                        update_query = """
                        UPDATE ward SET 
                            province_id = %s,
                            prefix = %s,
                            title = %s,
                            title_full = %s,
                            updated_at = %s,
                            updated_by = %s
                        WHERE pti_id = %s
                        """
                        cursor.execute(update_query, (
                            province_id, prefix, ward_title, title_full, 
                            current_time, 'import_script', ward_pti_id
                        ))
                        update_count += 1
                    else:
                        # Insert record mới
                        insert_query = """
                        INSERT INTO ward (
                            province_id, district_id, pti_id, prefix, title, title_full,
                            status, created_at, updated_at, created_by, is_merge
                        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        """
                        cursor.execute(insert_query, (
                            province_id, 0, ward_pti_id, prefix, ward_title, title_full,
                            2, current_time, current_time, 'import_script', 2
                        ))
                        insert_count += 1
                    
                    # Commit mỗi 100 records
                    if row_num % 100 == 0:
                        connection.commit()
                        print(f"📝 Đã xử lý {row_num} records...")
                        
                except Exception as e:
                    print(f"❌ Lỗi tại dòng {row_num}: {e}")
                    error_count += 1
                    continue
        
        # Commit cuối cùng
        connection.commit()
        
        print(f"\n🎉 Hoàn thành import!")
        print(f"✅ Inserted: {insert_count} records")
        print(f"🔄 Updated: {update_count} records") 
        print(f"❌ Errors: {error_count} records")
        print(f"📊 Total processed: {insert_count + update_count + error_count} records")
        
    except Exception as e:
        print(f"❌ Lỗi trong quá trình import: {e}")
        connection.rollback()
    
    finally:
        cursor.close()
        connection.close()
        print("🔌 Đã đóng kết nối database")

if __name__ == "__main__":
    import_ward_data()
