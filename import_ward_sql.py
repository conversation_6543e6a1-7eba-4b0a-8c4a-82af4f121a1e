#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import csv
import time

def generate_sql_import():
    """Tạo file SQL để import dữ liệu ward"""
    
    print("🔄 Tạo file SQL import...")
    
    current_time = int(time.time())
    sql_statements = []
    
    # Header
    sql_statements.append("-- Import ward data from ward_new.csv")
    sql_statements.append("-- Generated at: " + str(current_time))
    sql_statements.append("")
    
    try:
        with open('ward_new.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            
            insert_statements = []
            update_statements = []
            
            for row_num, row in enumerate(reader, 1):
                try:
                    # Mapping dữ liệu
                    ward_pti_id = int(row['ward_pti_id']) if row['ward_pti_id'].strip() else 0
                    prefix = row['prefix'].strip().replace("'", "\\'")
                    province_pti_id = int(row['province_pti_id']) if row['province_pti_id'].strip() else 0
                    province_title = row['province_title'].strip().replace("'", "\\'")
                    ward_title = row['ward_title'].strip().replace("'", "\\'")
                    province_id = int(row['province_id']) if row['province_id'].strip() else 0
                    
                    # Tạo title_full
                    title_full = f"{prefix} {ward_title}, {province_title}".replace("'", "\\'")
                    
                    # Tạo INSERT statement với ON DUPLICATE KEY UPDATE
                    insert_sql = f"""
INSERT INTO ward (
    province_id, district_id, pti_id, prefix, title, title_full,
    status, created_at, updated_at, created_by, is_merge
) VALUES (
    {province_id}, 0, {ward_pti_id}, '{prefix}', '{ward_title}', '{title_full}',
    2, {current_time}, {current_time}, 'import_script', 2
) ON DUPLICATE KEY UPDATE
    province_id = VALUES(province_id),
    prefix = VALUES(prefix),
    title = VALUES(title),
    title_full = VALUES(title_full),
    updated_at = {current_time},
    updated_by = 'import_script';"""
                    
                    insert_statements.append(insert_sql)
                    
                    if row_num % 100 == 0:
                        print(f"📝 Đã tạo SQL cho {row_num} records...")
                        
                except Exception as e:
                    print(f"❌ Lỗi tại dòng {row_num}: {e}")
                    continue
            
            # Gộp tất cả statements
            sql_statements.extend(insert_statements)
            
        # Ghi file SQL
        with open('import_ward.sql', 'w', encoding='utf-8') as f:
            f.write('\n'.join(sql_statements))
        
        print(f"✅ Đã tạo file import_ward.sql với {len(insert_statements)} statements")
        return True
        
    except Exception as e:
        print(f"❌ Lỗi khi tạo SQL: {e}")
        return False

def execute_sql_import():
    """Thực thi file SQL import"""
    
    print("\n🔄 Thực thi SQL import...")
    
    import subprocess
    
    try:
        # Chạy MySQL command
        cmd = [
            'mysql', 
            '-u', 'root', 
            '-proot', 
            '-h', 'localhost', 
            '-P', '3306',
            '-D', 'urbox',
            '-e', 'source import_ward.sql'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Import SQL thành công!")
            return True
        else:
            print(f"❌ Lỗi import SQL: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Lỗi khi thực thi SQL: {e}")
        return False

if __name__ == "__main__":
    if generate_sql_import():
        execute_sql_import()
