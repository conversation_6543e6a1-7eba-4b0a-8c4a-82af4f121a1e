#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import csv
import time

def create_insert_sql():
    """Tạo các c<PERSON><PERSON> lệnh INSERT SQL từ ward_new.csv"""
    
    print("🔄 Đọc dữ liệu từ ward_new.csv và tạo INSERT statements...")
    
    current_time = int(time.time())
    insert_statements = []
    
    try:
        with open('ward_new.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            
            for row_num, row in enumerate(reader, 1):
                try:
                    # Mapping dữ liệu từ CSV
                    ward_pti_id = int(row['ward_pti_id']) if row['ward_pti_id'].strip() else 0
                    prefix = row['prefix'].strip()
                    province_pti_id = int(row['province_pti_id']) if row['province_pti_id'].strip() else 0
                    province_title = row['province_title'].strip()
                    ward_title = row['ward_title'].strip()
                    province_id = int(row['province_id']) if row['province_id'].strip() else 0
                    
                    # Escape single quotes
                    prefix = prefix.replace("'", "\\'")
                    province_title = province_title.replace("'", "\\'")
                    ward_title = ward_title.replace("'", "\\'")
                    
                    # Tạo title_full
                    title_full = f"{prefix} {ward_title}, {province_title}".replace("'", "\\'")
                    
                    # Tạo INSERT statement với ON DUPLICATE KEY UPDATE
                    insert_sql = f"""INSERT INTO ward (
    province_id, district_id, pti_id, prefix, title, title_full,
    status, created_at, updated_at, created_by, is_merge
) VALUES (
    {province_id}, 0, {ward_pti_id}, '{prefix}', '{ward_title}', '{title_full}',
    2, {current_time}, {current_time}, 'import_script', 2
) ON DUPLICATE KEY UPDATE
    province_id = VALUES(province_id),
    prefix = VALUES(prefix),
    title = VALUES(title),
    title_full = VALUES(title_full),
    updated_at = {current_time},
    updated_by = 'import_script',
    is_merge = 2"""
                    
                    insert_statements.append(insert_sql)
                    
                    if row_num % 500 == 0:
                        print(f"📝 Đã tạo SQL cho {row_num} records...")
                        
                except Exception as e:
                    print(f"❌ Lỗi tại dòng {row_num}: {e}")
                    continue
        
        print(f"✅ Đã tạo {len(insert_statements)} INSERT statements")
        
        # Ghi ra file để backup
        with open('ward_insert_statements.sql', 'w', encoding='utf-8') as f:
            for i, stmt in enumerate(insert_statements):
                f.write(stmt)
                if i < len(insert_statements) - 1:
                    f.write(';\n\n')
                else:
                    f.write(';')
        
        print("💾 Đã lưu backup vào ward_insert_statements.sql")
        
        return insert_statements
        
    except Exception as e:
        print(f"❌ Lỗi khi đọc CSV: {e}")
        return []

def main():
    """Main function"""
    statements = create_insert_sql()
    
    if statements:
        print(f"\n📋 Đã chuẩn bị {len(statements)} câu lệnh INSERT")
        print("🔧 Bây giờ bạn có thể sử dụng MySQL MCP để thực thi từng câu lệnh")
        print("📁 File backup: ward_insert_statements.sql")
        
        # In ra 3 câu lệnh đầu tiên để xem
        print("\n📝 Preview 3 câu lệnh đầu tiên:")
        for i, stmt in enumerate(statements[:3]):
            print(f"\n--- Statement {i+1} ---")
            print(stmt[:200] + "..." if len(stmt) > 200 else stmt)
    else:
        print("❌ Không có dữ liệu để import")

if __name__ == "__main__":
    main()
