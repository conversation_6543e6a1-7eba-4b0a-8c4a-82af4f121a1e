Dự án này được tạo ra để xử lý việc gộp dữ liệu để tạo thành dữ liệu. 
** Tài liệu liên quan:
- ./table.md
** Bài toán:
- Việt Nam thực hiện sát nhập tỉnh thành và xã phường, bỏ cấp quận/huyện. do đó tôi sẽ không cần dùng tới bảng quận/huyện. xã phường sẽ là cấp con của tỉnh thành.
- bảng ___province sẽ chứa cả các dữ liệu tỉnh thành cũ và mới.
- bảng ward sẽ chứa cả các dữ liệu xã/phường cũ và mới.
cần lưu cả dữ liệu cũ và mới là do hệ thống đã chạy các dữ liẹu cũ rồi, khi gộp vào thì sẽ không sử dụng đến nữa nhưng vẫn cần lưu lại để kiểm tra.
- các dữ liệu cũ và mới sẽ được phân biệt bởi field is_merge: 1 là tỉnh thành và phường xã cũ, 2 là tỉnh thành và phường xã mới
dữ liệu hướng dẫn gộp xã phường ở tại bảng merge_ward, trong đó field title sẽ là tên phường xã mới. Description sẽ là hướng dẫn để biết Xã hiện tại được tạo nên từ các xã phường cũ nào. bảng ward và merge_ward sẽ map với nhau thông qua field pti_id(int) và is_merge=2

**Yêu cầu:
- từ bảng ___province, ward và merge_ward tôi muốn cập nhật lại dữ liệu của bảng brand_store như sau: 
  + thay đổi ward_id từ xã phường cũ sang xã phường mới
  + thay đổi title_ward từ tên xã phường cũ sang tên xã phường mới
- các thông tin sẽ được lấy như sau:
  + ward_id và title_ward sẽ được lấy từ bảng ward thông qua field pti_id và is_merge=2, bạn cần xem brand_store.ward_id và brand_store.ward_title hiện tại là gì, sau đó tìm trong bảng ward_merge thông qua descrition để biết được ward_id và title_ward mới là gì. nếu không rõ thông tin. hãy bỏ qua record đó và đánh dấu is_updated = 1. Nếu cập nhật được thì đánh dấu is_updated = 2
