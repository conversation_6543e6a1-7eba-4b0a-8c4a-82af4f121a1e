#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
import unicodedata

def create_slug(text):
    """Tạo slug từ text tiếng Việt"""
    # Loại bỏ dấu tiếng Việt
    text = unicodedata.normalize('NFD', text)
    text = ''.join(char for char in text if unicodedata.category(char) != 'Mn')
    
    # Chuyển thành lowercase
    text = text.lower()
    
    # Thay thế khoảng trắng và ký tự đặc biệt bằng dấu gạch ngang
    text = re.sub(r'[^a-z0-9]+', '-', text)
    
    # Loại bỏ dấu gạch ngang ở đầu và cuối
    text = text.strip('-')
    
    return text

def update_province_records():
    """Cập nhật các records có is_megre = 2"""
    
    print("🔄 Cập nhật records có is_megre = 2...")
    
    # <PERSON><PERSON> sách các tỉnh/thành phố cần cập nhật (đã sắp xếp theo alphabet)
    provinces = [
        (114, "Thành phố Cần Thơ"),
        (82, "Thành phố Hà Nội"), 
        (94, "Th<PERSON>nh ph<PERSON> <PERSON><PERSON><PERSON> <PERSON>òng"),
        (109, "Thành phố <PERSON>ồ Ch<PERSON>"),
        (101, "Thành ph<PERSON> <PERSON><PERSON>"),
        (102, "Th<PERSON>nh ph<PERSON> <PERSON><PERSON> Nẵng"),
        (113, "Tỉnh <PERSON> <PERSON>iang"),
        (92, "Tỉnh B<PERSON>c <PERSON>nh"),
        (115, "Tỉnh C<PERSON> Mau"),
        (83, "Tỉnh <PERSON> Bằng"),
        (104, "Tỉnh <PERSON>ia Lai"),
        (99, "Tỉnh Hà Tĩnh"),
        (95, "Tỉnh Hưng Yên"),
        (105, "Tỉnh Khánh Hòa"),
        (86, "Tỉnh Lai Châu"),
        (107, "Tỉnh Lâm Đồng"),
        (90, "Tỉnh Lạng Sơn"),
        (88, "Tỉnh Lào Cai"),
        (98, "Tỉnh Nghệ An"),
        (96, "Tỉnh Ninh Bình"),
        (93, "Tỉnh Phú Thọ"),
        (103, "Tỉnh Quảng Ngãi"),
        (91, "Tỉnh Quảng Ninh"),
        (100, "Tỉnh Quảng Trị"),
        (87, "Tỉnh Sơn La"),
        (110, "Tỉnh Tây Ninh"),
        (89, "Tỉnh Thái Nguyên"),
        (97, "Tỉnh Thanh Hóa"),
        (84, "Tỉnh Tuyên Quang"),
        (112, "Tỉnh Vĩnh Long"),
        (106, "Tỉnh Đắk Lắk"),
        (85, "Tỉnh Điện Biên"),
        (108, "Tỉnh Đồng Nai"),
        (111, "Tỉnh Đồng Tháp")
    ]
    
    # Sắp xếp theo alphabet
    provinces_sorted = sorted(provinces, key=lambda x: x[1])
    
    print(f"📊 Sẽ cập nhật {len(provinces_sorted)} records")
    
    # Tạo các câu lệnh UPDATE
    update_statements = []
    
    for position, (province_id, title) in enumerate(provinces_sorted, 1):
        # Tạo slug
        safe_title = create_slug(title)
        
        # Xác định is_city
        is_city = 1 if title.startswith("Thành phố") else 0
        
        # Tạo câu lệnh UPDATE
        update_sql = f"""UPDATE ___province SET 
    is_city = {is_city},
    position = {position},
    safe_title = '{safe_title}',
    updated_at = {int(__import__('time').time())},
    updated_by = 'update_script'
WHERE id = {province_id}"""
        
        update_statements.append(update_sql)
        
        print(f"📝 {position:2d}. ID {province_id}: {title} -> {safe_title} (is_city: {is_city})")
    
    # Ghi ra file SQL
    with open('update_province_statements.sql', 'w', encoding='utf-8') as f:
        f.write('-- Cập nhật records có is_megre = 2\n')
        f.write('-- Cập nhật is_city, position, safe_title\n\n')
        
        for i, stmt in enumerate(update_statements):
            f.write(f'-- Record {i+1}\n')
            f.write(stmt + ';\n\n')
    
    print(f"\n💾 Đã tạo file update_province_statements.sql với {len(update_statements)} câu lệnh UPDATE")
    
    return update_statements

if __name__ == "__main__":
    update_province_records()
